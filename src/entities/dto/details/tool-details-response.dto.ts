import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import { LearningCurve, PricingModel, PriceRange, TechnicalLevel } from '@generated-prisma';

export class ToolDetailsResponseDto {
  @ApiProperty({
    description: 'Entity ID (UUID) this detail record is associated with.',
    example: 'd4e5f6g7-h8i9-0123-4567-890123abcdef',
  })
  entityId: string;

  @ApiPropertyOptional({
    description:
      'Programming languages the tool is built with or primarily supports.',
    type: [String],
    example: ['Python', 'JavaScript'],
  })
  programmingLanguages?: any | null;

  @ApiPropertyOptional({
    description: 'Frameworks used by or compatible with the tool.',
    type: [String],
    example: ['React', 'Django'],
  })
  frameworks?: any | null;

  @ApiPropertyOptional({
    description: 'Libraries used by or compatible with the tool.',
    type: [String],
    example: ['TensorFlow', 'Pandas'],
  })
  libraries?: any | null;

  @ApiPropertyOptional({
    description: 'Known integrations with other tools or services.',
    type: [String],
    example: ['Slack', 'Google Drive'],
  })
  integrations?: any | null;

  @ApiPropertyOptional({
    description: 'Key features of the tool.',
    type: [String],
    example: ['Real-time collaboration', 'Advanced analytics'],
  })
  keyFeatures?: any | null;

  @ApiPropertyOptional({
    description: 'Common use cases for the tool.',
    type: [String],
    example: ['Data visualization', 'Project management'],
  })
  useCases?: any | null;

  @ApiPropertyOptional({
    description:
      'Target audience for the tool (e.g., developers, designers).',
    type: [String],
    example: ['Developers', 'Data Scientists'],
  })
  targetAudience?: any | null;

  @ApiPropertyOptional({
    enum: LearningCurve,
    description: 'Perceived learning curve for the tool.',
    example: LearningCurve.MEDIUM,
  })
  learningCurve?: LearningCurve | null;

  @ApiPropertyOptional({
    description: 'Options for deploying the tool.',
    type: [String],
    example: ['Cloud', 'On-premise', 'Docker'],
  })
  deploymentOptions?: any | null;

  @ApiPropertyOptional({
    description: 'Operating systems supported by the tool.',
    type: [String],
    example: ['Windows', 'macOS', 'Linux', 'Web'],
  })
  supportedOs?: any | null;

  @ApiPropertyOptional({
    description: 'Indicates if the tool has mobile support (iOS, Android).',
    example: true,
  })
  mobileSupport?: boolean | null;

  @ApiPropertyOptional({
    description: 'Indicates if the tool provides API access.',
    example: true,
  })
  apiAccess?: boolean | null;

  @ApiPropertyOptional({
    description: 'Level of customization available.',
    example: 'High',
  })
  customizationLevel?: string | null;

  @ApiPropertyOptional({
    description: 'Indicates if a free trial is available.',
    example: true,
  })
  trialAvailable?: boolean | null;

  @ApiPropertyOptional({
    description: 'Indicates if a demo is available.',
    example: true,
  })
  demoAvailable?: boolean | null;

  @ApiPropertyOptional({
    description: 'Indicates if the tool is open source.',
    example: false,
  })
  openSource?: boolean | null;

  @ApiPropertyOptional({
    description: 'Available support channels.',
    type: [String],
    example: ['Email', 'Chat', 'Phone', 'Community Forum'],
  })
  supportChannels?: any | null;

  @ApiProperty({
    description: 'Indicates if the tool has a free tier.',
    example: true,
    default: false,
  })
  hasFreeTier: boolean;

  @ApiPropertyOptional({
    enum: PricingModel,
    description: 'Pricing model of the tool.',
    example: PricingModel.FREEMIUM,
  })
  pricingModel?: PricingModel | null;

  @ApiPropertyOptional({
    enum: PriceRange,
    description: 'General price range of the tool.',
    example: PriceRange.MEDIUM,
  })
  priceRange?: PriceRange | null;

  @ApiPropertyOptional({
    description: 'Specific details about pricing.',
    example: 'Pro plan at $49/month.',
  })
  pricingDetails?: string | null;

  @ApiPropertyOptional({
    description: 'URL to the pricing page.',
    example: 'https://example.com/pricing',
  })
  pricingUrl?: string | null;

  @ApiPropertyOptional({
    description: 'Contact email for support.',
    example: '<EMAIL>',
  })
  supportEmail?: string | null;

  @ApiPropertyOptional({
    description: 'Indicates if live chat support is available.',
    example: true,
  })
  hasLiveChat?: boolean | null;

  @ApiPropertyOptional({
    description: 'URL to the community forum or Discord.',
    example: 'https://example.com/community',
  })
  communityUrl?: string | null;

  // Missing fields from recent DTO updates
  @ApiPropertyOptional({
    enum: TechnicalLevel,
    description: 'Technical level required to use the tool.',
    example: TechnicalLevel.INTERMEDIATE,
  })
  technicalLevel?: TechnicalLevel | null;

  @ApiPropertyOptional({
    description: 'Platforms supported by the tool.',
    type: [String],
    example: ['Web', 'macOS', 'iOS', 'Android'],
  })
  platforms?: any | null;

  @ApiPropertyOptional({
    description: 'Indicates if the tool provides an API.',
    example: true,
  })
  hasApi?: boolean | null;
}